# ZooKeeper配置文件
# 用于Kafka集群协调

# ZooKeeper数据目录
dataDir=/var/lib/zookeeper

# ZooKeeper客户端端口
clientPort=2181

# 最大客户端连接数
maxClientCnxns=0

# 管理端口
admin.enableServer=false

# 集群配置
# server.1=hadoop01:2888:3888
# server.2=hadoop02:2888:3888  
# server.3=hadoop03:2888:3888

# 单机模式配置（用于测试）
# 如果是集群模式，请取消注释上面的集群配置并注释下面的单机配置

# 心跳时间间隔（毫秒）
tickTime=2000

# 初始化连接时限
initLimit=10

# 同步时限
syncLimit=5

# 快照保留数量
autopurge.snapRetainCount=3

# 清理频率（小时）
autopurge.purgeInterval=1
