# Kafka Broker 3 配置文件 (hadoop03)

# Broker ID，集群中每个broker必须唯一
broker.id=3

# 监听地址和端口
listeners=PLAINTEXT://hadoop03:9092
advertised.listeners=PLAINTEXT://hadoop03:9092

# 日志目录
log.dirs=/var/lib/kafka-logs/broker-3

# ZooKeeper连接字符串
zookeeper.connect=hadoop01:2181,hadoop02:2181,hadoop03:2181

# 网络配置
num.network.threads=3
num.io.threads=8
socket.send.buffer.bytes=102400
socket.receive.buffer.bytes=102400
socket.request.max.bytes=104857600

# 日志配置
num.partitions=3
num.recovery.threads.per.data.dir=1
offsets.topic.replication.factor=3
transaction.state.log.replication.factor=3
transaction.state.log.min.isr=2

# 日志保留策略
log.retention.hours=168
log.retention.bytes=1073741824
log.segment.bytes=1073741824
log.retention.check.interval.ms=300000

# 压缩配置
log.cleanup.policy=delete
log.cleaner.enable=true

# 副本配置
default.replication.factor=3
min.insync.replicas=2

# 组协调器配置
group.initial.rebalance.delay.ms=0

# 连接超时
zookeeper.connection.timeout.ms=18000

# JMX端口（可选，用于监控）
# jmx.port=9999
