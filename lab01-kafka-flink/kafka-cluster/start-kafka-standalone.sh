#!/bin/bash

# Kafka单机版启动脚本（用于测试）

set -e

echo "=== 启动Kafka单机版 ==="

# 检查环境变量
if [ -z "$KAFKA_HOME" ]; then
    echo "错误: KAFKA_HOME环境变量未设置"
    echo "请先运行 source ~/.bashrc 或重新登录"
    exit 1
fi

# 创建必要的目录
sudo mkdir -p /var/lib/kafka-logs
sudo mkdir -p /var/lib/zookeeper
sudo chown -R $USER:$USER /var/lib/kafka-logs /var/lib/zookeeper

# 创建单机版配置文件
cat > $KAFKA_HOME/config/zookeeper-standalone.properties << EOF
dataDir=/var/lib/zookeeper
clientPort=2181
maxClientCnxns=0
admin.enableServer=false
tickTime=2000
initLimit=10
syncLimit=5
EOF

cat > $KAFKA_HOME/config/server-standalone.properties << EOF
broker.id=0
listeners=PLAINTEXT://0.0.0.0:9092
advertised.listeners=PLAINTEXT://hadoop01:9092
log.dirs=/var/lib/kafka-logs
zookeeper.connect=localhost:2181
num.network.threads=3
num.io.threads=8
socket.send.buffer.bytes=102400
socket.receive.buffer.bytes=102400
socket.request.max.bytes=104857600
num.partitions=1
num.recovery.threads.per.data.dir=1
offsets.topic.replication.factor=1
transaction.state.log.replication.factor=1
transaction.state.log.min.isr=1
log.retention.hours=168
log.retention.bytes=1073741824
log.segment.bytes=1073741824
log.retention.check.interval.ms=300000
log.cleanup.policy=delete
log.cleaner.enable=true
default.replication.factor=1
min.insync.replicas=1
group.initial.rebalance.delay.ms=0
zookeeper.connection.timeout.ms=18000
EOF

# 启动ZooKeeper
echo "启动ZooKeeper..."
$KAFKA_HOME/bin/zookeeper-server-start.sh -daemon $KAFKA_HOME/config/zookeeper-standalone.properties

# 等待ZooKeeper启动
sleep 5

# 启动Kafka
echo "启动Kafka..."
$KAFKA_HOME/bin/kafka-server-start.sh -daemon $KAFKA_HOME/config/server-standalone.properties

# 等待服务启动
sleep 10

# 检查服务状态
echo "检查服务状态..."
if pgrep -f "kafka.Kafka" > /dev/null; then
    echo "✓ Kafka服务已启动"
else
    echo "✗ Kafka服务启动失败"
    exit 1
fi

if pgrep -f "QuorumPeerMain" > /dev/null; then
    echo "✓ ZooKeeper服务已启动"
else
    echo "✗ ZooKeeper服务启动失败"
    exit 1
fi

echo "Kafka单机版启动完成！"
echo "可以使用以下命令测试:"
echo "  创建主题: $KAFKA_HOME/bin/kafka-topics.sh --create --topic test --bootstrap-server hadoop01:9092 --partitions 1 --replication-factor 1"
echo "  查看主题: $KAFKA_HOME/bin/kafka-topics.sh --list --bootstrap-server hadoop01:9092"
echo "  生产消息: $KAFKA_HOME/bin/kafka-console-producer.sh --topic test --bootstrap-server hadoop01:9092"
echo "  消费消息: $KAFKA_HOME/bin/kafka-console-consumer.sh --topic test --from-beginning --bootstrap-server hadoop01:9092"
