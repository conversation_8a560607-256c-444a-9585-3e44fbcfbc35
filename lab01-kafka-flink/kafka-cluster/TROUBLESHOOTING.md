# Kafka "Timed out waiting for a node assignment" 错误解决方案

## 问题描述
运行 `start-kafka-standalone.sh` 后，在创建Kafka主题时出现以下错误：
```
Error while executing topic command : Timed out waiting for a node assignment. Call: createTopics
```

## 问题原因
1. **配置不匹配**：应用程序配置为连接集群模式的Kafka brokers (`hadoop01:9092,hadoop02:9092,hadoop03:9092`)，但实际运行的是单机版Kafka
2. **主机名解析问题**：`/etc/hosts` 文件中的主机名映射不正确，`hadoop01` 被解析到错误的IP地址
3. **Kafka服务器配置**：`advertised.listeners` 配置与客户端期望的地址不匹配

## 解决方案

### 1. 修复主机名解析
```bash
# 删除错误的主机名映射
sudo sed -i '/************[1-3]/d' /etc/hosts

# 添加正确的主机名映射
echo "127.0.0.1 hadoop01" | sudo tee -a /etc/hosts
echo "127.0.0.1 hadoop02" | sudo tee -a /etc/hosts
echo "127.0.0.1 hadoop03" | sudo tee -a /etc/hosts

# 验证主机名解析
ping -c 1 hadoop01
```

### 2. 修改Kafka单机版配置
在 `start-kafka-standalone.sh` 中修改服务器配置：
```bash
# 修改前
listeners=PLAINTEXT://localhost:9092
advertised.listeners=PLAINTEXT://localhost:9092

# 修改后
listeners=PLAINTEXT://0.0.0.0:9092
advertised.listeners=PLAINTEXT://hadoop01:9092
```

### 3. 重启Kafka服务
```bash
# 停止Kafka
$KAFKA_HOME/bin/kafka-server-stop.sh

# 等待几秒
sleep 3

# 重新启动Kafka
cd lab01-kafka-flink/kafka-cluster
./start-kafka-standalone.sh
```

### 4. 验证修复
```bash
# 测试主题创建
$KAFKA_HOME/bin/kafka-topics.sh --create --topic test --bootstrap-server hadoop01:9092 --partitions 1 --replication-factor 1

# 列出主题
$KAFKA_HOME/bin/kafka-topics.sh --list --bootstrap-server hadoop01:9092
```

## 单机版应用启动脚本
为了方便使用，已创建单机版的应用启动脚本：

### 消息源软件（单机版）
```bash
cd lab01-kafka-flink/message-source
./run-message-source-standalone.sh
```

### 推荐系统（单机版）
```bash
cd lab01-kafka-flink/recommendation-system
./run-recommendation-system-standalone.sh
```

## 预防措施
1. **环境一致性**：确保所有配置文件中的broker地址保持一致
2. **主机名管理**：定期检查 `/etc/hosts` 文件，避免重复或冲突的条目
3. **网络测试**：在启动应用前，先测试主机名解析和网络连接

## 常用调试命令
```bash
# 检查Kafka进程
ps aux | grep kafka | grep -v grep

# 检查端口监听
netstat -tlnp | grep 9092

# 检查主机名解析
nslookup hadoop01

# 查看Kafka日志
tail -f $KAFKA_HOME/logs/server.log

# 测试Kafka连接
$KAFKA_HOME/bin/kafka-broker-api-versions.sh --bootstrap-server hadoop01:9092
```
