#!/bin/bash

# Kafka集群启动脚本

set -e

echo "=== 启动Kafka集群 ==="

# 检查环境变量
if [ -z "$KAFKA_HOME" ]; then
    echo "错误: KAFKA_HOME环境变量未设置"
    echo "请先运行 source ~/.bashrc 或重新登录"
    exit 1
fi

# 获取当前主机名
HOSTNAME=$(hostname)
echo "当前主机: $HOSTNAME"

# 创建必要的目录
sudo mkdir -p /var/lib/kafka-logs/broker-{1,2,3}
sudo mkdir -p /var/lib/zookeeper
sudo chown -R $USER:$USER /var/lib/kafka-logs /var/lib/zookeeper

# 复制配置文件到Kafka目录
cp zookeeper.properties $KAFKA_HOME/config/
cp server-*.properties $KAFKA_HOME/config/

# 根据主机名确定要启动的服务
case $HOSTNAME in
    "hadoop01")
        echo "在hadoop01上启动ZooKeeper和Kafka Broker 1"

        # 启动ZooKeeper
        echo "启动ZooKeeper..."
        $KAFKA_HOME/bin/zookeeper-server-start.sh -daemon $KAFKA_HOME/config/zookeeper.properties

        # 等待ZooKeeper启动
        sleep 5

        # 启动Kafka Broker 1
        echo "启动Kafka Broker 1..."
        $KAFKA_HOME/bin/kafka-server-start.sh -daemon $KAFKA_HOME/config/server-1.properties
        ;;

    "hadoop02")
        echo "在hadoop02上启动Kafka Broker 2"

        # 等待ZooKeeper可用
        echo "等待ZooKeeper可用..."
        while ! netcat -z hadoop01 2181 2>/dev/null; do
            sleep 1
        done

        # 启动Kafka Broker 2
        echo "启动Kafka Broker 2..."
        $KAFKA_HOME/bin/kafka-server-start.sh -daemon $KAFKA_HOME/config/server-2.properties
        ;;

    "hadoop03")
        echo "在hadoop03上启动Kafka Broker 3"

        # 等待ZooKeeper可用
        echo "等待ZooKeeper可用..."
        while ! netcat -z hadoop01 2181 2>/dev/null; do
            sleep 1
        done

        # 启动Kafka Broker 3
        echo "启动Kafka Broker 3..."
        $KAFKA_HOME/bin/kafka-server-start.sh -daemon $KAFKA_HOME/config/server-3.properties
        ;;

    *)
        echo "未知主机名: $HOSTNAME"
        echo "支持的主机名: hadoop01, hadoop02, hadoop03"
        echo "如果是单机测试，请使用 start-kafka-standalone.sh"
        exit 1
        ;;
esac

# 等待服务启动
sleep 10

# 检查服务状态
echo "检查服务状态..."
if pgrep -f "kafka.Kafka" > /dev/null; then
    echo "✓ Kafka服务已启动"
else
    echo "✗ Kafka服务启动失败"
    exit 1
fi

if [ "$HOSTNAME" = "hadoop01" ]; then
    if pgrep -f "QuorumPeerMain" > /dev/null; then
        echo "✓ ZooKeeper服务已启动"
    else
        echo "✗ ZooKeeper服务启动失败"
        exit 1
    fi
fi

echo "Kafka集群启动完成！"
echo "可以使用以下命令测试集群:"
echo "  创建主题: $KAFKA_HOME/bin/kafka-topics.sh --create --topic test --bootstrap-server hadoop01:9092,hadoop02:9092,hadoop03:9092 --partitions 3 --replication-factor 3"
echo "  查看主题: $KAFKA_HOME/bin/kafka-topics.sh --list --bootstrap-server hadoop01:9092,hadoop02:9092,hadoop03:9092"
