#!/bin/bash

# Flink集群启动脚本

set -e

echo "=== 启动Flink集群 ==="

# 检查环境变量
if [ -z "$FLINK_HOME" ]; then
    echo "错误: FLINK_HOME环境变量未设置"
    echo "请先运行 source ~/.bashrc 或重新登录"
    exit 1
fi

# 获取当前主机名
HOSTNAME=$(hostname)
echo "当前主机: $HOSTNAME"

# 创建必要的目录
sudo mkdir -p /tmp/flink-checkpoints
sudo mkdir -p /tmp/flink-savepoints
sudo mkdir -p /tmp/flink-web-upload
sudo mkdir -p /tmp/flink-completed-jobs
sudo mkdir -p /tmp/flink-logs
sudo chown -R $USER:$USER /tmp/flink-*

# 复制配置文件到Flink目录
cp flink-conf.yaml $FLINK_HOME/conf/
cp workers $FLINK_HOME/conf/
cp masters $FLINK_HOME/conf/

# 根据主机名确定要启动的服务
case $HOSTNAME in
    "hadoop01")
        echo "在hadoop01上启动JobManager"

        # 启动JobManager
        echo "启动JobManager..."
        $FLINK_HOME/bin/jobmanager.sh start

        # 启动TaskManager
        echo "启动本地TaskManager..."
        $FLINK_HOME/bin/taskmanager.sh start

        # 启动历史服务器
        echo "启动历史服务器..."
        $FLINK_HOME/bin/historyserver.sh start
        ;;

    "hadoop02"|"hadoop03")
        echo "在$HOSTNAME上启动TaskManager"

        # 等待JobManager可用
        echo "等待JobManager可用..."
        while ! netcat -z hadoop01 6123 2>/dev/null; do
            sleep 1
        done

        # 启动TaskManager
        echo "启动TaskManager..."
        $FLINK_HOME/bin/taskmanager.sh start
        ;;

    *)
        echo "未知主机名: $HOSTNAME"
        echo "支持的主机名: hadoop01, hadoop02, hadoop03"
        echo "如果是单机测试，请使用 start-flink-standalone.sh"
        exit 1
        ;;
esac

# 等待服务启动
sleep 10

# 检查服务状态
echo "检查服务状态..."

if [ "$HOSTNAME" = "hadoop01" ]; then
    if pgrep -f "StandaloneSessionClusterEntrypoint" > /dev/null; then
        echo "✓ JobManager已启动"
    else
        echo "✗ JobManager启动失败"
        exit 1
    fi

    if pgrep -f "HistoryServer" > /dev/null; then
        echo "✓ 历史服务器已启动"
    else
        echo "✗ 历史服务器启动失败"
    fi
fi

if pgrep -f "TaskManagerRunner" > /dev/null; then
    echo "✓ TaskManager已启动"
else
    echo "✗ TaskManager启动失败"
    exit 1
fi

echo "Flink集群启动完成！"
if [ "$HOSTNAME" = "hadoop01" ]; then
    echo "Web UI地址: http://hadoop01:8081"
    echo "历史服务器: http://hadoop01:8082"
fi
echo "可以使用以下命令提交作业:"
echo "  $FLINK_HOME/bin/flink run examples/streaming/WordCount.jar"
