#!/bin/bash

# Flink单机版启动脚本（用于测试）

set -e

echo "=== 启动Flink单机版 ==="

# 检查环境变量
if [ -z "$FLINK_HOME" ]; then
    echo "错误: FLINK_HOME环境变量未设置"
    echo "请先运行 source ~/.bashrc 或重新登录"
    exit 1
fi

# 创建必要的目录
sudo mkdir -p /tmp/flink-checkpoints
sudo mkdir -p /tmp/flink-savepoints
sudo mkdir -p /tmp/flink-web-upload
sudo mkdir -p /tmp/flink-completed-jobs
sudo mkdir -p /tmp/flink-logs
sudo chown -R $USER:$USER /tmp/flink-*

# 创建单机版配置文件
cat > $FLINK_HOME/conf/flink-conf-standalone.yaml << EOF
# Flink单机版配置

# JobManager配置
jobmanager.rpc.address: localhost
jobmanager.rpc.port: 6123
jobmanager.bind-host: 0.0.0.0
jobmanager.memory.process.size: 1600m

# TaskManager配置
taskmanager.bind-host: 0.0.0.0
taskmanager.host: localhost
taskmanager.rpc.port: 6122
taskmanager.memory.process.size: 1728m
taskmanager.numberOfTaskSlots: 4

# 并行度配置
parallelism.default: 1

# 检查点配置
state.backend: hashmap
state.checkpoints.dir: file:///tmp/flink-checkpoints
state.savepoints.dir: file:///tmp/flink-savepoints

# Web UI配置
web.submit.enable: true
web.cancel.enable: true
web.upload.dir: /tmp/flink-web-upload

# 历史服务器配置
historyserver.web.address: 0.0.0.0
historyserver.web.port: 8082
historyserver.archive.fs.dir: file:///tmp/flink-completed-jobs

# 重启策略
restart-strategy: fixed-delay
restart-strategy.fixed-delay.attempts: 3
restart-strategy.fixed-delay.delay: 10 s

# 网络配置
taskmanager.network.memory.fraction: 0.1
taskmanager.network.memory.min: 64mb
taskmanager.network.memory.max: 1gb

# 日志配置
env.log.dir: /tmp/flink-logs
EOF

# 备份原配置文件
if [ -f "$FLINK_HOME/conf/flink-conf.yaml" ]; then
    cp $FLINK_HOME/conf/flink-conf.yaml $FLINK_HOME/conf/flink-conf.yaml.backup
fi

# 使用单机版配置
cp $FLINK_HOME/conf/flink-conf-standalone.yaml $FLINK_HOME/conf/flink-conf.yaml

# 启动Flink集群
echo "启动Flink单机版集群..."
$FLINK_HOME/bin/start-cluster.sh

# 等待服务启动
sleep 10

# 检查服务状态
echo "检查服务状态..."

if pgrep -f "StandaloneSessionClusterEntrypoint" > /dev/null; then
    echo "✓ JobManager已启动"
else
    echo "✗ JobManager启动失败"
    exit 1
fi

if pgrep -f "TaskManagerRunner" > /dev/null; then
    echo "✓ TaskManager已启动"
else
    echo "✗ TaskManager启动失败"
    exit 1
fi

echo "Flink单机版启动完成！"
echo "Web UI地址: http://localhost:8081"
echo "可以使用以下命令测试:"
echo "  $FLINK_HOME/bin/flink run $FLINK_HOME/examples/streaming/WordCount.jar"
echo "  $FLINK_HOME/bin/flink list"
echo "停止集群: $FLINK_HOME/bin/stop-cluster.sh"
