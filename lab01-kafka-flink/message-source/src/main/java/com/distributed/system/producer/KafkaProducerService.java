package com.distributed.system.producer;

import com.distributed.system.JsonUtils;
import com.distributed.system.model.Product;
import com.distributed.system.model.UserBehavior;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.apache.kafka.common.serialization.StringSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;
import java.util.concurrent.Future;

/**
 * Kafka生产者服务
 */
public class KafkaProducerService {
    
    private static final Logger LOG = LoggerFactory.getLogger(KafkaProducerService.class);
    
    private final KafkaProducer<String, String> producer;
    private final String productTopic;
    private final String userBehaviorTopic;
    
    public KafkaProducerService(String bootstrapServers, String productTopic, String userBehaviorTopic) {
        this.productTopic = productTopic;
        this.userBehaviorTopic = userBehaviorTopic;
        
        Properties props = new Properties();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        
        // 生产者配置优化
        props.put(ProducerConfig.ACKS_CONFIG, "all"); // 等待所有副本确认
        props.put(ProducerConfig.RETRIES_CONFIG, 3); // 重试次数
        props.put(ProducerConfig.BATCH_SIZE_CONFIG, 16384); // 批处理大小
        props.put(ProducerConfig.LINGER_MS_CONFIG, 1); // 等待时间
        props.put(ProducerConfig.BUFFER_MEMORY_CONFIG, 33554432); // 缓冲区大小
        props.put(ProducerConfig.COMPRESSION_TYPE_CONFIG, "snappy"); // 压缩类型
        
        // 幂等性配置
        props.put(ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG, true);
        
        this.producer = new KafkaProducer<>(props);
        
        LOG.info("Kafka生产者初始化完成，连接到: {}", bootstrapServers);
    }
    
    /**
     * 发送商品事件
     */
    public Future<RecordMetadata> sendProductEvent(Product product) {
        try {
            String json = JsonUtils.toJson(product);
            if (json == null) {
                LOG.error("商品对象序列化失败: {}", product);
                return null;
            }
            
            ProducerRecord<String, String> record = new ProducerRecord<>(
                    productTopic,
                    product.getProductId(),
                    json
            );
            
            Future<RecordMetadata> future = producer.send(record, (metadata, exception) -> {
                if (exception != null) {
                    LOG.error("发送商品事件失败: {}", product.getProductId(), exception);
                } else {
                    LOG.info("商品事件发送成功: {} -> partition: {}, offset: {}", 
                            product.getProductId(), metadata.partition(), metadata.offset());
                }
            });
            
            return future;
            
        } catch (Exception e) {
            LOG.error("发送商品事件异常: {}", product, e);
            return null;
        }
    }
    
    /**
     * 发送用户行为事件
     */
    public Future<RecordMetadata> sendUserBehaviorEvent(UserBehavior behavior) {
        try {
            String json = JsonUtils.toJson(behavior);
            if (json == null) {
                LOG.error("用户行为对象序列化失败: {}", behavior);
                return null;
            }
            
            ProducerRecord<String, String> record = new ProducerRecord<>(
                    userBehaviorTopic,
                    behavior.getUserId(),
                    json
            );
            
            Future<RecordMetadata> future = producer.send(record, (metadata, exception) -> {
                if (exception != null) {
                    LOG.error("发送用户行为事件失败: {} - {}", behavior.getUserId(), behavior.getBehaviorType(), exception);
                } else {
                    LOG.info("用户行为事件发送成功: {} - {} -> partition: {}, offset: {}", 
                            behavior.getUserId(), behavior.getBehaviorType(), metadata.partition(), metadata.offset());
                }
            });
            
            return future;
            
        } catch (Exception e) {
            LOG.error("发送用户行为事件异常: {}", behavior, e);
            return null;
        }
    }
    
    /**
     * 刷新缓冲区
     */
    public void flush() {
        producer.flush();
    }
    
    /**
     * 关闭生产者
     */
    public void close() {
        try {
            producer.close();
            LOG.info("Kafka生产者已关闭");
        } catch (Exception e) {
            LOG.error("关闭Kafka生产者失败", e);
        }
    }
}
