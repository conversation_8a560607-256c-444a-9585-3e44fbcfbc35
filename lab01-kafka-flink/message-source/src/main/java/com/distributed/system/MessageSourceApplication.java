package com.distributed.system;

import com.distributed.system.consumer.KafkaConsumerService;
import com.distributed.system.producer.KafkaProducerService;
import com.distributed.system.ui.CommandLineInterface;
import org.apache.commons.cli.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 消息源软件主应用程序
 */
public class MessageSourceApplication {
    
    private static final Logger LOG = LoggerFactory.getLogger(MessageSourceApplication.class);
    
    // 默认配置
    private static final String DEFAULT_KAFKA_BROKERS = "hadoop01:9092,hadoop02:9092,hadoop03:9092";
    private static final String DEFAULT_PRODUCT_TOPIC = "product-events";
    private static final String DEFAULT_USER_BEHAVIOR_TOPIC = "user-behavior-events";
    private static final String DEFAULT_RECOMMENDATION_TOPIC = "recommendation-results";
    private static final String DEFAULT_CONSUMER_GROUP = "message-source-group";
    
    public static void main(String[] args) {
        try {
            // 解析命令行参数
            CommandLine cmd = parseCommandLine(args);
            
            // 获取配置参数
            String kafkaBrokers = cmd.getOptionValue("brokers", DEFAULT_KAFKA_BROKERS);
            String productTopic = cmd.getOptionValue("product-topic", DEFAULT_PRODUCT_TOPIC);
            String userBehaviorTopic = cmd.getOptionValue("behavior-topic", DEFAULT_USER_BEHAVIOR_TOPIC);
            String recommendationTopic = cmd.getOptionValue("recommendation-topic", DEFAULT_RECOMMENDATION_TOPIC);
            String consumerGroup = cmd.getOptionValue("group", DEFAULT_CONSUMER_GROUP);
            
            LOG.info("启动消息源软件...");
            LOG.info("Kafka Brokers: {}", kafkaBrokers);
            LOG.info("商品事件主题: {}", productTopic);
            LOG.info("用户行为主题: {}", userBehaviorTopic);
            LOG.info("推荐结果主题: {}", recommendationTopic);
            LOG.info("消费者组: {}", consumerGroup);
            
            // 创建Kafka生产者服务
            KafkaProducerService producer = new KafkaProducerService(
                    kafkaBrokers, productTopic, userBehaviorTopic);
            
            // 创建Kafka消费者服务
            KafkaConsumerService consumer = new KafkaConsumerService(
                    kafkaBrokers, recommendationTopic, consumerGroup);
            
            // 添加关闭钩子
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                LOG.info("接收到关闭信号，正在关闭应用程序...");
                try {
                    consumer.stop();
                    producer.close();
                } catch (Exception e) {
                    LOG.error("关闭应用程序时发生错误", e);
                }
            }));
            
            // 启动命令行界面
            CommandLineInterface cli = new CommandLineInterface(producer, consumer);
            cli.start();
            
        } catch (Exception e) {
            LOG.error("应用程序启动失败", e);
            System.err.println("应用程序启动失败: " + e.getMessage());
            System.exit(1);
        }
    }
    
    /**
     * 解析命令行参数
     */
    private static CommandLine parseCommandLine(String[] args) throws ParseException {
        Options options = new Options();
        
        options.addOption(Option.builder("b")
                .longOpt("brokers")
                .hasArg()
                .desc("Kafka broker地址列表 (默认: " + DEFAULT_KAFKA_BROKERS + ")")
                .build());
        
        options.addOption(Option.builder("p")
                .longOpt("product-topic")
                .hasArg()
                .desc("商品事件主题名称 (默认: " + DEFAULT_PRODUCT_TOPIC + ")")
                .build());
        
        options.addOption(Option.builder("u")
                .longOpt("behavior-topic")
                .hasArg()
                .desc("用户行为主题名称 (默认: " + DEFAULT_USER_BEHAVIOR_TOPIC + ")")
                .build());
        
        options.addOption(Option.builder("r")
                .longOpt("recommendation-topic")
                .hasArg()
                .desc("推荐结果主题名称 (默认: " + DEFAULT_RECOMMENDATION_TOPIC + ")")
                .build());
        
        options.addOption(Option.builder("g")
                .longOpt("group")
                .hasArg()
                .desc("消费者组ID (默认: " + DEFAULT_CONSUMER_GROUP + ")")
                .build());
        
        options.addOption(Option.builder("h")
                .longOpt("help")
                .desc("显示帮助信息")
                .build());
        
        CommandLineParser parser = new DefaultParser();
        
        try {
            CommandLine cmd = parser.parse(options, args);
            
            if (cmd.hasOption("help")) {
                printHelp(options);
                System.exit(0);
            }
            
            return cmd;
            
        } catch (ParseException e) {
            System.err.println("命令行参数解析失败: " + e.getMessage());
            printHelp(options);
            throw e;
        }
    }
    
    /**
     * 打印帮助信息
     */
    private static void printHelp(Options options) {
        HelpFormatter formatter = new HelpFormatter();
        formatter.printHelp("java -jar message-source.jar", 
                "分布式实时电商推荐系统 - 消息源软件", options, 
                "\n示例:\n" +
                "  java -jar message-source.jar\n" +
                "  java -jar message-source.jar -b localhost:9092 -g my-group\n" +
                "  java -jar message-source.jar --brokers hadoop01:9092,hadoop02:9092 --group test-group\n");
    }
}
