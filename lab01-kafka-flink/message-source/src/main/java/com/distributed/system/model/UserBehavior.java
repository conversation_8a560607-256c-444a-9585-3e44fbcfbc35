package com.distributed.system.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.Objects;

/**
 * 用户行为数据模型
 */
public class UserBehavior implements Serializable {
    
    @JsonProperty("userId")
    private String userId;
    
    @JsonProperty("productId")
    private String productId;
    
    @JsonProperty("behaviorType")
    private String behaviorType; // view, click, purchase, add_to_cart
    
    @JsonProperty("timestamp")
    private Long timestamp;
    
    @JsonProperty("sessionId")
    private String sessionId;
    
    @JsonProperty("category")
    private String category;
    
    @JsonProperty("price")
    private Double price;
    
    // 默认构造函数
    public UserBehavior() {}
    
    public UserBehavior(String userId, String productId, String behaviorType, String sessionId) {
        this.userId = userId;
        this.productId = productId;
        this.behaviorType = behaviorType;
        this.sessionId = sessionId;
        this.timestamp = System.currentTimeMillis();
    }
    
    public UserBehavior(String userId, String productId, String behaviorType, String sessionId, 
                       String category, Double price) {
        this.userId = userId;
        this.productId = productId;
        this.behaviorType = behaviorType;
        this.sessionId = sessionId;
        this.category = category;
        this.price = price;
        this.timestamp = System.currentTimeMillis();
    }
    
    // Getter和Setter方法
    public String getUserId() {
        return userId;
    }
    
    public void setUserId(String userId) {
        this.userId = userId;
    }
    
    public String getProductId() {
        return productId;
    }
    
    public void setProductId(String productId) {
        this.productId = productId;
    }
    
    public String getBehaviorType() {
        return behaviorType;
    }
    
    public void setBehaviorType(String behaviorType) {
        this.behaviorType = behaviorType;
    }
    
    public Long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }
    
    public String getSessionId() {
        return sessionId;
    }
    
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
    
    public Double getPrice() {
        return price;
    }
    
    public void setPrice(Double price) {
        this.price = price;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserBehavior that = (UserBehavior) o;
        return Objects.equals(userId, that.userId) &&
                Objects.equals(productId, that.productId) &&
                Objects.equals(behaviorType, that.behaviorType) &&
                Objects.equals(timestamp, that.timestamp);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(userId, productId, behaviorType, timestamp);
    }
    
    @Override
    public String toString() {
        return "UserBehavior{" +
                "userId='" + userId + '\'' +
                ", productId='" + productId + '\'' +
                ", behaviorType='" + behaviorType + '\'' +
                ", timestamp=" + timestamp +
                ", sessionId='" + sessionId + '\'' +
                ", category='" + category + '\'' +
                ", price=" + price +
                '}';
    }
}
