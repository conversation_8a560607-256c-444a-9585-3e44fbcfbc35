package com.distributed.system.ui;

import com.distributed.system.consumer.KafkaConsumerService;
import com.distributed.system.model.Product;
import com.distributed.system.model.UserBehavior;
import com.distributed.system.producer.KafkaProducerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;
import java.util.Random;
import java.util.Scanner;
import java.util.UUID;

/**
 * 命令行界面
 */
public class CommandLineInterface {
    
    private static final Logger LOG = LoggerFactory.getLogger(CommandLineInterface.class);
    
    private final KafkaProducerService producer;
    private final KafkaConsumerService consumer;
    private final Scanner scanner;
    private final Random random;
    
    // 模拟数据
    private final List<String> categories = Arrays.asList(
            "电子产品", "服装", "家居", "图书", "运动", "美妆", "食品", "汽车用品"
    );
    
    private final List<String> productNames = Arrays.asList(
            "智能手机", "笔记本电脑", "无线耳机", "运动鞋", "连衣裙", "咖啡机",
            "小说", "护肤品", "零食", "车载充电器", "瑜伽垫", "台灯"
    );
    
    private final List<String> behaviorTypes = Arrays.asList(
            "view", "click", "add_to_cart", "purchase"
    );
    
    public CommandLineInterface(KafkaProducerService producer, KafkaConsumerService consumer) {
        this.producer = producer;
        this.consumer = consumer;
        this.scanner = new Scanner(System.in);
        this.random = new Random();
    }
    
    /**
     * 启动命令行界面
     */
    public void start() {
        System.out.println("\n" + "=".repeat(80));
        System.out.println("🚀 分布式实时电商推荐系统 - 消息源软件");
        System.out.println("=".repeat(80));
        
        // 启动推荐结果消费者
        consumer.start();
        
        boolean running = true;
        while (running) {
            showMenu();
            String choice = scanner.nextLine().trim();
            
            switch (choice) {
                case "1":
                    createProduct();
                    break;
                case "2":
                    simulateUserBehavior();
                    break;
                case "3":
                    batchSimulateUserBehavior();
                    break;
                case "4":
                    showStatus();
                    break;
                case "5":
                    running = false;
                    break;
                default:
                    System.out.println("❌ 无效选择，请重新输入");
            }
        }
        
        shutdown();
    }
    
    /**
     * 显示菜单
     */
    private void showMenu() {
        System.out.println("\n📋 请选择操作:");
        System.out.println("1. 创建新商品");
        System.out.println("2. 模拟用户行为");
        System.out.println("3. 批量模拟用户行为");
        System.out.println("4. 查看系统状态");
        System.out.println("5. 退出系统");
        System.out.print("请输入选择 (1-5): ");
    }
    
    /**
     * 创建商品
     */
    private void createProduct() {
        System.out.println("\n📦 创建新商品");
        System.out.println("-".repeat(40));
        
        try {
            System.out.print("商品名称 (回车使用随机名称): ");
            String name = scanner.nextLine().trim();
            if (name.isEmpty()) {
                name = productNames.get(random.nextInt(productNames.size()));
            }
            
            System.out.print("商品类别 (回车使用随机类别): ");
            String category = scanner.nextLine().trim();
            if (category.isEmpty()) {
                category = categories.get(random.nextInt(categories.size()));
            }
            
            System.out.print("商品价格 (回车使用随机价格): ");
            String priceStr = scanner.nextLine().trim();
            double price;
            if (priceStr.isEmpty()) {
                price = 10.0 + random.nextDouble() * 990.0; // 10-1000之间的随机价格
            } else {
                price = Double.parseDouble(priceStr);
            }
            
            System.out.print("商品描述 (回车使用默认描述): ");
            String description = scanner.nextLine().trim();
            if (description.isEmpty()) {
                description = "这是一个优质的" + category + "商品";
            }
            
            String productId = "prod_" + UUID.randomUUID().toString().substring(0, 8);
            Product product = new Product(productId, name, category, price, description);
            
            producer.sendProductEvent(product);
            producer.flush();
            
            System.out.printf("✅ 商品创建成功! ID: %s, 名称: %s, 类别: %s, 价格: %.2f%n", 
                    productId, name, category, price);
            
        } catch (Exception e) {
            System.out.println("❌ 创建商品失败: " + e.getMessage());
            LOG.error("创建商品失败", e);
        }
    }
    
    /**
     * 模拟用户行为
     */
    private void simulateUserBehavior() {
        System.out.println("\n👤 模拟用户行为");
        System.out.println("-".repeat(40));
        
        try {
            System.out.print("用户ID (回车使用随机用户): ");
            String userId = scanner.nextLine().trim();
            if (userId.isEmpty()) {
                userId = "user_" + random.nextInt(1000);
            }
            
            System.out.print("商品ID (回车使用随机商品): ");
            String productId = scanner.nextLine().trim();
            if (productId.isEmpty()) {
                productId = "prod_" + UUID.randomUUID().toString().substring(0, 8);
            }
            
            System.out.print("行为类型 [view/click/add_to_cart/purchase] (回车使用随机行为): ");
            String behaviorType = scanner.nextLine().trim();
            if (behaviorType.isEmpty()) {
                behaviorType = behaviorTypes.get(random.nextInt(behaviorTypes.size()));
            }
            
            String sessionId = "session_" + UUID.randomUUID().toString().substring(0, 8);
            String category = categories.get(random.nextInt(categories.size()));
            double price = 10.0 + random.nextDouble() * 990.0;
            
            UserBehavior behavior = new UserBehavior(userId, productId, behaviorType, sessionId, category, price);
            
            producer.sendUserBehaviorEvent(behavior);
            producer.flush();
            
            System.out.printf("✅ 用户行为模拟成功! 用户: %s, 商品: %s, 行为: %s%n", 
                    userId, productId, behaviorType);
            
        } catch (Exception e) {
            System.out.println("❌ 模拟用户行为失败: " + e.getMessage());
            LOG.error("模拟用户行为失败", e);
        }
    }
    
    /**
     * 批量模拟用户行为
     */
    private void batchSimulateUserBehavior() {
        System.out.println("\n🔄 批量模拟用户行为");
        System.out.println("-".repeat(40));
        
        try {
            System.out.print("模拟用户数量 (默认10): ");
            String userCountStr = scanner.nextLine().trim();
            int userCount = userCountStr.isEmpty() ? 10 : Integer.parseInt(userCountStr);
            
            System.out.print("每个用户的行为数量 (默认5): ");
            String behaviorCountStr = scanner.nextLine().trim();
            int behaviorCount = behaviorCountStr.isEmpty() ? 5 : Integer.parseInt(behaviorCountStr);
            
            System.out.println("开始批量模拟...");
            
            for (int i = 0; i < userCount; i++) {
                String userId = "user_" + (1000 + i);
                String sessionId = "session_" + UUID.randomUUID().toString().substring(0, 8);
                
                for (int j = 0; j < behaviorCount; j++) {
                    String productId = "prod_" + UUID.randomUUID().toString().substring(0, 8);
                    String behaviorType = behaviorTypes.get(random.nextInt(behaviorTypes.size()));
                    String category = categories.get(random.nextInt(categories.size()));
                    double price = 10.0 + random.nextDouble() * 990.0;
                    
                    UserBehavior behavior = new UserBehavior(userId, productId, behaviorType, sessionId, category, price);
                    producer.sendUserBehaviorEvent(behavior);
                    
                    // 添加小延迟，模拟真实用户行为
                    Thread.sleep(100);
                }
                
                System.out.printf("✅ 用户 %s 的行为模拟完成 (%d 个行为)%n", userId, behaviorCount);
            }
            
            producer.flush();
            System.out.printf("🎉 批量模拟完成! 总计: %d 个用户, %d 个行为%n", 
                    userCount, userCount * behaviorCount);
            
        } catch (Exception e) {
            System.out.println("❌ 批量模拟失败: " + e.getMessage());
            LOG.error("批量模拟失败", e);
        }
    }
    
    /**
     * 显示系统状态
     */
    private void showStatus() {
        System.out.println("\n📊 系统状态");
        System.out.println("-".repeat(40));
        System.out.printf("推荐结果消费者状态: %s%n", consumer.isRunning() ? "✅ 运行中" : "❌ 已停止");
        System.out.println("Kafka生产者状态: ✅ 运行中");
        System.out.println("系统运行正常");
    }
    
    /**
     * 关闭系统
     */
    private void shutdown() {
        System.out.println("\n🔄 正在关闭系统...");
        
        try {
            consumer.stop();
            producer.close();
            scanner.close();
            
            System.out.println("✅ 系统已安全关闭");
            
        } catch (Exception e) {
            System.out.println("❌ 关闭系统时发生错误: " + e.getMessage());
            LOG.error("关闭系统失败", e);
        }
    }
}
