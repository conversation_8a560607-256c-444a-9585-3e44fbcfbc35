package com.distributed.system.consumer;

import com.distributed.system.JsonUtils;
import com.distributed.system.model.Recommendation;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.util.Collections;
import java.util.Properties;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Kafka消费者服务 - 接收推荐结果
 */
public class KafkaConsumerService {
    
    private static final Logger LOG = LoggerFactory.getLogger(KafkaConsumerService.class);
    
    private final KafkaConsumer<String, String> consumer;
    private final String recommendationTopic;
    private final AtomicBoolean running = new AtomicBoolean(false);
    private Thread consumerThread;
    
    public KafkaConsumerService(String bootstrapServers, String recommendationTopic, String groupId) {
        this.recommendationTopic = recommendationTopic;
        
        Properties props = new Properties();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        props.put(ConsumerConfig.GROUP_ID_CONFIG, groupId);
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        
        // 消费者配置
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, true);
        props.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG, 1000);
        props.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, 30000);
        props.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG, 3000);
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, 500);
        props.put(ConsumerConfig.FETCH_MIN_BYTES_CONFIG, 1);
        props.put(ConsumerConfig.FETCH_MAX_WAIT_MS_CONFIG, 500);
        
        this.consumer = new KafkaConsumer<>(props);
        
        LOG.info("Kafka消费者初始化完成，连接到: {}, 主题: {}, 组ID: {}", 
                bootstrapServers, recommendationTopic, groupId);
    }
    
    /**
     * 启动消费者
     */
    public void start() {
        if (running.get()) {
            LOG.warn("消费者已经在运行中");
            return;
        }
        
        running.set(true);
        consumer.subscribe(Collections.singletonList(recommendationTopic));
        
        consumerThread = new Thread(this::consumeMessages, "RecommendationConsumer");
        consumerThread.start();
        
        LOG.info("推荐结果消费者已启动");
    }
    
    /**
     * 停止消费者
     */
    public void stop() {
        if (!running.get()) {
            return;
        }
        
        running.set(false);
        
        if (consumerThread != null) {
            try {
                consumerThread.interrupt();
                consumerThread.join(5000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        
        try {
            consumer.close();
            LOG.info("Kafka消费者已关闭");
        } catch (Exception e) {
            LOG.error("关闭Kafka消费者失败", e);
        }
    }
    
    /**
     * 消费消息的主循环
     */
    private void consumeMessages() {
        try {
            while (running.get()) {
                ConsumerRecords<String, String> records = consumer.poll(Duration.ofMillis(1000));
                
                for (ConsumerRecord<String, String> record : records) {
                    try {
                        processRecommendation(record);
                    } catch (Exception e) {
                        LOG.error("处理推荐结果失败: {}", record.value(), e);
                    }
                }
            }
        } catch (Exception e) {
            if (running.get()) {
                LOG.error("消费消息异常", e);
            }
        } finally {
            LOG.info("推荐结果消费者线程结束");
        }
    }
    
    /**
     * 处理推荐结果
     */
    private void processRecommendation(ConsumerRecord<String, String> record) {
        String json = record.value();
        Recommendation recommendation = JsonUtils.fromJson(json, Recommendation.class);
        
        if (recommendation == null) {
            LOG.error("推荐结果反序列化失败: {}", json);
            return;
        }
        
        // 打印推荐结果到控制台
        printRecommendation(recommendation);
        
        // 这里可以添加其他处理逻辑，比如：
        // 1. 存储到数据库
        // 2. 发送给前端界面
        // 3. 触发其他业务流程
    }
    
    /**
     * 打印推荐结果
     */
    private void printRecommendation(Recommendation recommendation) {
        System.out.println("\n" + "=".repeat(80));
        System.out.println("🎯 收到新的推荐结果!");
        System.out.println("=".repeat(80));
        System.out.printf("👤 用户ID: %s%n", recommendation.getUserId());
        System.out.printf("🔗 会话ID: %s%n", recommendation.getSessionId());
        System.out.printf("🤖 算法: %s%n", recommendation.getAlgorithm());
        System.out.printf("📊 置信度: %.2f%n", recommendation.getConfidence());
        System.out.printf("💡 推荐原因: %s%n", recommendation.getReason());
        System.out.printf("⏰ 时间: %s%n", new java.util.Date(recommendation.getTimestamp()));
        
        System.out.println("\n🛍️ 推荐商品列表:");
        for (int i = 0; i < recommendation.getRecommendedProducts().size(); i++) {
            System.out.printf("  %d. %s%n", i + 1, recommendation.getRecommendedProducts().get(i));
        }
        
        System.out.println("=".repeat(80) + "\n");
        
        // 同时记录到日志
        LOG.info("收到推荐结果 - 用户: {}, 商品数量: {}, 置信度: {}", 
                recommendation.getUserId(), 
                recommendation.getRecommendedProducts().size(),
                recommendation.getConfidence());
    }
    
    /**
     * 检查消费者是否在运行
     */
    public boolean isRunning() {
        return running.get();
    }
}
