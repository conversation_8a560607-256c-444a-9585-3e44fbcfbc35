package raft

import (
	"bytes"
	"encoding/gob"
	"log"
	"math/rand"
	"reflect"
	"strings"
	"sync"
	"sync/atomic"
	"time"
)

// ClientEnd represents an RPC client endpoint
type ClientEnd struct {
	endname interface{} // this end-point's name
	ch      chan reqMsg // copy of Network.endCh
	done    chan struct{}
}

type reqMsg struct {
	endname  interface{} // name of sending ClientEnd
	svcMeth  string      // e.g. "Raft.AppendEntries"
	argsType reflect.Type
	args     []byte
	replyCh  chan replyMsg
}

type replyMsg struct {
	ok    bool
	reply []byte
}

// Call invokes the named function, waits for it to complete, and returns its error status
func (e *ClientEnd) Call(svcMeth string, args interface{}, reply interface{}) bool {
	req := reqMsg{}
	req.endname = e.endname
	req.svcMeth = svcMeth
	req.argsType = reflect.TypeOf(args)
	req.replyCh = make(chan replyMsg)

	qb := new(bytes.Buffer)
	qe := gob.NewEncoder(qb)
	if err := qe.Encode(args); err != nil {
		panic(err)
	}
	req.args = qb.Bytes()

	select {
	case e.ch <- req:
		// ok
	case <-e.done:
		return false
	}

	rep := <-req.replyCh
	if rep.ok {
		rb := bytes.NewBuffer(rep.reply)
		rd := gob.NewDecoder(rb)
		if err := rd.Decode(reply); err != nil {
			log.Fatalf("ClientEnd.Call(): decode reply: %v\n", err)
		}
		return true
	} else {
		return false
	}
}

// Network simulates a network for testing
type Network struct {
	mu             sync.Mutex
	reliable       bool
	longDelays     bool                        // pause a long time on send on disabled connection
	longReordering bool                        // sometimes delay replies a long time
	ends           map[interface{}]*ClientEnd  // ends, by name
	enabled        map[interface{}]bool        // by end name
	servers        map[interface{}]*Server     // servers, by name
	connections    map[interface{}]interface{} // endname -> servername
	endCh          chan reqMsg
	done           chan struct{} // closed when Network is cleaned up
	count          int32         // total RPC count, for statistics
	bytes          int64         // total bytes send, for statistics
}

// MakeNetwork creates a new network
func MakeNetwork() *Network {
	rn := &Network{}
	rn.reliable = true
	rn.ends = map[interface{}]*ClientEnd{}
	rn.enabled = map[interface{}]bool{}
	rn.servers = map[interface{}]*Server{}
	rn.connections = map[interface{}]interface{}{}
	rn.endCh = make(chan reqMsg)
	rn.done = make(chan struct{})

	// single goroutine to handle all ClientEnd.Call()s
	go func() {
		for {
			select {
			case xreq := <-rn.endCh:
				atomic.AddInt32(&rn.count, 1)
				atomic.AddInt64(&rn.bytes, int64(len(xreq.args)))
				go rn.ProcessReq(xreq)
			case <-rn.done:
				return
			}
		}
	}()

	return rn
}

// ProcessReq processes an RPC request
func (rn *Network) ProcessReq(req reqMsg) {
	rn.mu.Lock()

	enabled := rn.enabled[req.endname]
	servername := rn.connections[req.endname]
	server := rn.servers[servername]

	rn.mu.Unlock()

	if !enabled || server == nil {
		// simulate no reply and eventual timeout.
		ms := 0
		if rn.longDelays {
			// let Raft tests check that leader doesn't send
			// RPCs synchronously.
			ms = (rand.Int() % 7000)
		} else {
			// many kv tests require the client to try each
			// server in fairly rapid succession.
			ms = (rand.Int() % 100)
		}
		time.Sleep(time.Duration(ms) * time.Millisecond)
		req.replyCh <- replyMsg{false, nil}
		return
	}

	// execute the service; it's supposed to declare an argument
	// that's a pointer to the reply struct.
	ech := make(chan replyMsg)
	go func() {
		r := server.dispatch(req)
		ech <- r
	}()

	// wait for handler to return,
	// but stop waiting if DeleteServer() has been called,
	// and return an error.
	var reply replyMsg
	replyOK := false
	serverDead := false
	for !replyOK && !serverDead {
		select {
		case reply = <-ech:
			replyOK = true
		case <-server.done:
			serverDead = true
		}
	}

	// do not reply if DeleteServer() has been called, i.e.
	// the server has been killed. this is needed to avoid
	// situation in which a client gets a positive reply
	// to an Append, but the server persisted the update
	// into the old disk image, which may not survive a crash.
	serverDead = serverDead || server.isdead()

	if !replyOK || serverDead {
		// server was killed while we were waiting; return error.
		req.replyCh <- replyMsg{false, nil}
	} else if !rn.reliable && (rand.Int()%1000) < 100 {
		// drop the reply, return as if timeout
		req.replyCh <- replyMsg{false, nil}
	} else if rn.longReordering && rand.Intn(900) < 600 {
		// delay the response for a while
		ms := 200 + rand.Intn(1+rand.Intn(2000))
		// Russ points out that this timer arrangement will decrease
		// the number of goroutines, so that the race
		// detector is less likely to get upset.
		time.AfterFunc(time.Duration(ms)*time.Millisecond, func() {
			atomic.AddInt64(&rn.bytes, int64(len(reply.reply)))
			req.replyCh <- reply
		})
	} else {
		atomic.AddInt64(&rn.bytes, int64(len(reply.reply)))
		req.replyCh <- reply
	}
}

// MakeEnd creates a new client endpoint
func (rn *Network) MakeEnd(endname interface{}) *ClientEnd {
	rn.mu.Lock()
	defer rn.mu.Unlock()

	if _, ok := rn.ends[endname]; ok {
		log.Fatalf("MakeEnd: %v already exists\n", endname)
	}

	e := &ClientEnd{}
	e.endname = endname
	e.ch = rn.endCh
	e.done = rn.done
	rn.ends[endname] = e
	rn.enabled[endname] = false
	rn.connections[endname] = nil

	return e
}

// AddServer adds a server to the network
func (rn *Network) AddServer(servername interface{}, rs *Server) {
	rn.mu.Lock()
	defer rn.mu.Unlock()

	rn.servers[servername] = rs
}

// DeleteServer removes a server from the network
func (rn *Network) DeleteServer(servername interface{}) {
	rn.mu.Lock()
	defer rn.mu.Unlock()

	rn.servers[servername] = nil
}

// Connect connects an endpoint to a server
func (rn *Network) Connect(endname interface{}, servername interface{}) {
	rn.mu.Lock()
	defer rn.mu.Unlock()

	rn.connections[endname] = servername
}

// Enable enables/disables an endpoint
func (rn *Network) Enable(endname interface{}, enabled bool) {
	rn.mu.Lock()
	defer rn.mu.Unlock()

	rn.enabled[endname] = enabled
}

// Reliable sets whether the network is reliable
func (rn *Network) Reliable(yes bool) {
	rn.mu.Lock()
	defer rn.mu.Unlock()

	rn.reliable = yes
}

// LongReordering enables/disables long reordering delays
func (rn *Network) LongReordering(yes bool) {
	rn.mu.Lock()
	defer rn.mu.Unlock()

	rn.longReordering = yes
}

// LongDelays enables/disables long delays
func (rn *Network) LongDelays(yes bool) {
	rn.mu.Lock()
	defer rn.mu.Unlock()

	rn.longDelays = yes
}

// GetCount returns the total number of RPCs
func (rn *Network) GetCount(servername interface{}) int {
	rn.mu.Lock()
	defer rn.mu.Unlock()

	return int(atomic.LoadInt32(&rn.count))
}

// GetTotalBytes returns the total bytes sent
func (rn *Network) GetTotalBytes() int64 {
	rn.mu.Lock()
	defer rn.mu.Unlock()

	return atomic.LoadInt64(&rn.bytes)
}

// Cleanup cleans up the network
func (rn *Network) Cleanup() {
	close(rn.done)
}

// Server represents an RPC server
type Server struct {
	mu       sync.Mutex
	services map[string]*Service
	count    int // incoming RPCs
	done     chan struct{}
	dead     int32 // for testing -- atomically
}

// MakeServer creates a new server
func MakeServer() *Server {
	rs := &Server{}
	rs.services = map[string]*Service{}
	rs.done = make(chan struct{})
	return rs
}

// AddService adds a service to the server
func (rs *Server) AddService(svc *Service) {
	rs.mu.Lock()
	defer rs.mu.Unlock()
	rs.services[svc.name] = svc
}

// dispatch handles an RPC request
func (rs *Server) dispatch(req reqMsg) replyMsg {
	rs.mu.Lock()

	rs.count += 1

	// split Raft.AppendEntries into service and method
	dot := strings.LastIndex(req.svcMeth, ".")
	serviceName := req.svcMeth[:dot]
	methodName := req.svcMeth[dot+1:]

	service, ok := rs.services[serviceName]

	rs.mu.Unlock()

	if ok {
		return service.dispatch(methodName, req)
	} else {
		choices := []string{}
		for k := range rs.services {
			choices = append(choices, k)
		}
		log.Fatalf("labrpc.Server.dispatch(): unknown service %v in %v.%v; expecting one of %v\n",
			serviceName, serviceName, methodName, choices)
		return replyMsg{false, nil}
	}
}

// GetCount returns the number of incoming RPCs
func (rs *Server) GetCount() int {
	rs.mu.Lock()
	defer rs.mu.Unlock()
	return rs.count
}

// isdead checks if the server is dead
func (rs *Server) isdead() bool {
	return atomic.LoadInt32(&rs.dead) != 0
}

// Service represents an RPC service
type Service struct {
	name    string
	rcvr    reflect.Value
	typ     reflect.Type
	methods map[string]reflect.Method
}

// MakeService creates a new service
func MakeService(rcvr interface{}) *Service {
	svc := &Service{}
	svc.typ = reflect.TypeOf(rcvr)
	svc.rcvr = reflect.ValueOf(rcvr)
	svc.name = reflect.Indirect(svc.rcvr).Type().Name()
	svc.methods = map[string]reflect.Method{}

	for m := 0; m < svc.typ.NumMethod(); m++ {
		method := svc.typ.Method(m)
		mtype := method.Type
		mname := method.Name

		if method.PkgPath != "" || // capitalized?
			mtype.NumIn() != 3 ||
			mtype.In(2).Kind() != reflect.Ptr ||
			mtype.NumOut() != 0 {
			// the method is not suitable for a handler
			//fmt.Printf("bad method: %v\n", mname)
		} else {
			// the method looks like a handler
			svc.methods[mname] = method
		}
	}

	return svc
}

// dispatch handles a method call
func (svc *Service) dispatch(methname string, req reqMsg) replyMsg {
	if method, ok := svc.methods[methname]; ok {
		// prepare space into which to read the argument.
		// the Value's type will be a pointer to req.argsType.
		args := reflect.New(req.argsType)

		// decode the argument.
		ab := bytes.NewBuffer(req.args)
		ad := gob.NewDecoder(ab)
		ad.Decode(args.Interface())

		// allocate space for the reply.
		replyType := method.Type.In(2)
		replyType = replyType.Elem()
		replyv := reflect.New(replyType)

		// call the method.
		function := method.Func
		function.Call([]reflect.Value{svc.rcvr, args.Elem(), replyv})

		// encode the reply.
		rb := new(bytes.Buffer)
		re := gob.NewEncoder(rb)
		re.EncodeValue(replyv)

		return replyMsg{true, rb.Bytes()}
	} else {
		choices := []string{}
		for k := range svc.methods {
			choices = append(choices, k)
		}
		log.Fatalf("labrpc.Service.dispatch(): unknown method %v in %v; expecting one of %v\n",
			methname, req.svcMeth, choices)
		return replyMsg{false, nil}
	}
}
