#!/bin/bash

# Raft测试运行脚本

echo "=== Raft 一致性协议测试 ==="
echo

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "错误: 未找到Go环境，请先安装Go"
    exit 1
fi

# 进入项目目录
cd "$(dirname "$0")"

# 安装依赖
echo "安装依赖..."
go mod tidy
echo

# 运行2A测试 - 选举
echo "=== 运行2A测试: Raft选举 ==="
echo "测试初始选举..."
go test -run TestInitialElection2A -v
echo

echo "测试重新选举..."
go test -run TestReElection2A -v
echo

echo "测试多次选举..."
go test -run TestManyElections2A -v
echo

# 运行2B测试 - 日志复制
echo "=== 运行2B测试: Raft日志复制 ==="
echo "测试基本一致性..."
go test -run TestBasicAgree2B -v
echo

echo "测试RPC字节数..."
go test -run TestRPCBytes2B -v
echo

echo "测试跟随者重连后的一致性..."
go test -run TestFailAgree2B -v
echo

echo "测试无法达成一致的情况..."
go test -run TestFailNoAgree2B -v
echo

echo "测试并发Start()调用..."
go test -run TestConcurrentStarts2B -v
echo

# 运行所有测试
echo "=== 运行所有测试 ==="
go test -v

echo
echo "=== 测试完成 ==="

# 可选：运行压力测试
read -p "是否运行压力测试? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "运行压力测试 (10次)..."
    for i in {1..10}; do
        echo "第 $i 次测试..."
        go test -run 2A > /dev/null 2>&1
        if [ $? -ne 0 ]; then
            echo "第 $i 次测试失败!"
            break
        fi
    done
    echo "压力测试完成"
fi
