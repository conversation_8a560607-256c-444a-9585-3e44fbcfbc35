# Lab 2: Raft 一致性协议实现

这是MIT 6.824分布式系统课程Lab 2的Raft一致性协议实现。

## 项目结构

```
lab02-raft/
├── raft.go          # Raft算法核心实现
├── labrpc.go        # RPC通信模拟框架
├── persister.go     # 持久化存储接口
├── config.go        # 测试配置和工具
├── raft_test.go     # 测试用例
├── go.mod           # Go模块定义
└── README.md        # 项目说明
```

## 实现功能

### 2A: Raft选举
- [x] 领导者选举机制
- [x] 心跳机制
- [x] 任期管理
- [x] 随机选举超时

### 2B: Raft日志复制
- [x] 日志条目复制
- [x] 日志一致性检查
- [x] 提交索引管理
- [x] 日志回退优化

### 2C: Raft持久化
- [x] 持久化状态保存
- [x] 崩溃恢复
- [x] 状态恢复

### 2D: Raft日志压缩
- [x] 快照机制
- [x] InstallSnapshot RPC
- [x] 日志截断

## 运行测试

### 安装依赖
```bash
cd lab02-raft
go mod tidy
```

### 运行所有测试
```bash
go test -v
```

### 运行特定测试
```bash
# 2A: 选举测试
go test -run 2A -v

# 2B: 日志复制测试
go test -run 2B -v

# 2C: 持久化测试
go test -run 2C -v

# 2D: 快照测试
go test -run 2D -v
```

### 运行压力测试
```bash
# 运行多次测试检查稳定性
for i in {1..10}; do go test -run 2A; done
```

## 核心算法说明

### 服务器状态
- **Follower**: 跟随者，被动接收来自领导者的消息
- **Candidate**: 候选者，发起选举请求投票
- **Leader**: 领导者，处理客户端请求并复制日志

### 关键RPC
1. **RequestVote**: 候选者请求投票
2. **AppendEntries**: 领导者复制日志条目（也用作心跳）
3. **InstallSnapshot**: 领导者发送快照给落后的跟随者

### 持久化状态
- `currentTerm`: 当前任期
- `votedFor`: 当前任期投票给的候选者
- `log[]`: 日志条目数组
- `lastIncludedIndex`: 快照包含的最后一个日志索引
- `lastIncludedTerm`: 快照包含的最后一个日志任期

### 易失状态
- `commitIndex`: 已提交的最高日志索引
- `lastApplied`: 已应用到状态机的最高日志索引
- `nextIndex[]`: 对每个服务器，下一个要发送的日志索引
- `matchIndex[]`: 对每个服务器，已知已复制的最高日志索引

## 设计要点

### 选举安全性
- 每个任期最多选出一个领导者
- 使用随机选举超时避免选票分裂
- 候选者必须拥有最新的日志才能当选

### 日志匹配
- 如果两个日志在相同索引和任期有相同条目，则之前所有条目都相同
- 领导者强制跟随者复制自己的日志

### 领导者完整性
- 如果某个日志条目在给定任期被提交，那么该条目将出现在所有更高任期的领导者日志中

### 状态机安全性
- 如果服务器在给定索引应用了日志条目，其他服务器不会在相同索引应用不同的条目

## 性能优化

1. **日志回退优化**: 使用冲突索引和冲突任期快速回退
2. **批量发送**: 一次AppendEntries可以发送多个日志条目
3. **并行复制**: 并行向所有跟随者发送日志
4. **快照压缩**: 定期创建快照减少日志大小

## 测试说明

测试用例覆盖了以下场景：
- 基本选举和重新选举
- 网络分区和恢复
- 并发客户端请求
- 服务器崩溃和重启
- 日志不一致处理
- 快照安装和恢复

## 注意事项

1. 所有RPC调用都是异步的，需要正确处理并发
2. 持久化操作必须在回复RPC之前完成
3. 选举超时必须随机化以避免活锁
4. 日志索引从1开始，索引0用作哨兵
5. 快照必须原子性地应用到状态机

## 参考资料

- [Raft论文](https://raft.github.io/raft.pdf)
- [MIT 6.824课程](http://nil.csail.mit.edu/6.824/2022/labs/lab-raft.html)
- [Raft可视化](https://raft.github.io/)
- [学生指南](https://thesquareplanet.com/blog/students-guide-to-raft/)
