package raft

import (
	"bytes"
	"encoding/gob"
	"log"
	"math/rand"
	"sync"
	"sync/atomic"
	"time"
)

// ApplyMsg represents a message to be applied to the state machine
type ApplyMsg struct {
	CommandValid bool
	Command      interface{}
	CommandIndex int

	// For 2D:
	SnapshotValid bool
	Snapshot      []byte
	SnapshotTerm  int
	SnapshotIndex int
}

// LogEntry represents a single log entry
type LogEntry struct {
	Command interface{}
	Term    int
}

// ServerState represents the state of a Raft server
type ServerState int

const (
	Follower ServerState = iota
	Candidate
	Leader
)

// Constants
const (
	InvalidId         = -1
	HeartBeatInterval = 100 * time.Millisecond
	ElectionTimeout   = 300 * time.Millisecond
)

// Raft represents a single Raft peer
type Raft struct {
	mu        sync.Mutex          // Lock to protect shared access to this peer's state
	peers     []*ClientEnd        // RPC end points of all peers
	persister *Persister          // Object to hold this peer's persisted state
	me        int                 // this peer's index into peers[]
	dead      int32               // set by Kill()

	// Persistent state on all servers
	currentTerm int        // latest term server has seen
	votedFor    int        // candidateId that received vote in current term
	log         []LogEntry // log entries

	// Volatile state on all servers
	commitIndex int // index of highest log entry known to be committed
	lastApplied int // index of highest log entry applied to state machine

	// Volatile state on leaders
	nextIndex  []int // for each server, index of the next log entry to send
	matchIndex []int // for each server, index of highest log entry known to be replicated

	// Custom fields
	state    ServerState
	leaderId int

	// Timers
	electionTimer  *time.Timer
	heartbeatTimer *time.Timer

	// Channels
	applyCh chan ApplyMsg

	// Snapshot state (for 2D)
	lastIncludedIndex int
	lastIncludedTerm  int
	snapshot          []byte
}

// GetState returns currentTerm and whether this server believes it is the leader
func (rf *Raft) GetState() (int, bool) {
	rf.mu.Lock()
	defer rf.mu.Unlock()
	term := rf.currentTerm
	isleader := rf.state == Leader
	return term, isleader
}

// persist saves Raft's persistent state to stable storage
func (rf *Raft) persist() {
	w := new(bytes.Buffer)
	e := gob.NewEncoder(w)
	e.Encode(rf.currentTerm)
	e.Encode(rf.votedFor)
	e.Encode(rf.log)
	e.Encode(rf.lastIncludedIndex)
	e.Encode(rf.lastIncludedTerm)
	data := w.Bytes()
	rf.persister.SaveRaftState(data)
}

// readPersist restores previously persisted state
func (rf *Raft) readPersist(data []byte) {
	if data == nil || len(data) < 1 {
		return
	}

	r := bytes.NewBuffer(data)
	d := gob.NewDecoder(r)
	var currentTerm int
	var votedFor int
	var raftLog []LogEntry
	var lastIncludedIndex int
	var lastIncludedTerm int

	if d.Decode(&currentTerm) != nil ||
		d.Decode(&votedFor) != nil ||
		d.Decode(&raftLog) != nil ||
		d.Decode(&lastIncludedIndex) != nil ||
		d.Decode(&lastIncludedTerm) != nil {
		log.Fatalf("Failed to read persist")
	} else {
		rf.currentTerm = currentTerm
		rf.votedFor = votedFor
		rf.log = raftLog
		rf.lastIncludedIndex = lastIncludedIndex
		rf.lastIncludedTerm = lastIncludedTerm
		rf.commitIndex = rf.lastIncludedIndex
	}
}

// killed returns whether this server has been killed
func (rf *Raft) killed() bool {
	z := atomic.LoadInt32(&rf.dead)
	return z == 1
}

// Kill is called by the tester when a Raft instance won't be needed again
func (rf *Raft) Kill() {
	atomic.StoreInt32(&rf.dead, 1)
	// Your code here, if desired.
}

// Make creates a new Raft server instance
func Make(peers []*ClientEnd, me int, persister *Persister, applyCh chan ApplyMsg) *Raft {
	rf := &Raft{}
	rf.peers = peers
	rf.persister = persister
	rf.me = me
	rf.applyCh = applyCh

	// Initialize state
	rf.currentTerm = 0
	rf.votedFor = InvalidId
	rf.log = make([]LogEntry, 1) // log index starts at 1
	rf.log[0] = LogEntry{Term: 0} // dummy entry at index 0
	rf.commitIndex = 0
	rf.lastApplied = 0
	rf.state = Follower
	rf.leaderId = InvalidId
	rf.lastIncludedIndex = 0
	rf.lastIncludedTerm = 0

	// Initialize from state persisted before a crash
	rf.readPersist(persister.ReadRaftState())
	rf.snapshot = persister.ReadSnapshot()

	// Start election timer
	rf.resetElectionTimer()

	// Start background goroutines
	go rf.ticker()
	go rf.applier()

	return rf
}

// RequestVoteArgs represents arguments for RequestVote RPC
type RequestVoteArgs struct {
	Term         int // candidate's term
	CandidateId  int // candidate requesting vote
	LastLogIndex int // index of candidate's last log entry
	LastLogTerm  int // term of candidate's last log entry
}

// RequestVoteReply represents reply for RequestVote RPC
type RequestVoteReply struct {
	Term        int  // currentTerm, for candidate to update itself
	VoteGranted bool // true means candidate received vote
}

// RequestVote RPC handler
func (rf *Raft) RequestVote(args *RequestVoteArgs, reply *RequestVoteReply) {
	rf.mu.Lock()
	defer rf.mu.Unlock()

	reply.Term = rf.currentTerm
	reply.VoteGranted = false

	// Reply false if term < currentTerm
	if args.Term < rf.currentTerm {
		return
	}

	// If RPC request contains term T > currentTerm: set currentTerm = T, convert to follower
	if args.Term > rf.currentTerm {
		rf.currentTerm = args.Term
		rf.votedFor = InvalidId
		rf.state = Follower
		rf.leaderId = InvalidId
		rf.persist()
	}

	// Check if we can vote for this candidate
	lastLogIndex := len(rf.log) - 1 + rf.lastIncludedIndex
	lastLogTerm := rf.lastIncludedTerm
	if lastLogIndex > rf.lastIncludedIndex {
		lastLogTerm = rf.log[lastLogIndex-rf.lastIncludedIndex].Term
	}

	// Vote if:
	// 1. Haven't voted for anyone else in this term, or already voted for this candidate
	// 2. Candidate's log is at least as up-to-date as receiver's log
	upToDate := args.LastLogTerm > lastLogTerm ||
		(args.LastLogTerm == lastLogTerm && args.LastLogIndex >= lastLogIndex)

	if (rf.votedFor == InvalidId || rf.votedFor == args.CandidateId) && upToDate {
		rf.votedFor = args.CandidateId
		rf.state = Follower
		rf.leaderId = InvalidId
		reply.VoteGranted = true
		rf.resetElectionTimer()
		rf.persist()
	}

	reply.Term = rf.currentTerm
}

// sendRequestVote sends a RequestVote RPC to a server
func (rf *Raft) sendRequestVote(server int, args *RequestVoteArgs, reply *RequestVoteReply) bool {
	ok := rf.peers[server].Call("Raft.RequestVote", args, reply)
	return ok
}

// AppendEntriesArgs represents arguments for AppendEntries RPC
type AppendEntriesArgs struct {
	Term         int        // leader's term
	LeaderId     int        // so follower can redirect clients
	PrevLogIndex int        // index of log entry immediately preceding new ones
	PrevLogTerm  int        // term of prevLogIndex entry
	Entries      []LogEntry // log entries to store (empty for heartbeat)
	LeaderCommit int        // leader's commitIndex
}

// AppendEntriesReply represents reply for AppendEntries RPC
type AppendEntriesReply struct {
	Term          int  // currentTerm, for leader to update itself
	Success       bool // true if follower contained entry matching prevLogIndex and prevLogTerm
	ConflictIndex int  // optimization for log backtracking
	ConflictTerm  int  // optimization for log backtracking
}

// AppendEntries RPC handler
func (rf *Raft) AppendEntries(args *AppendEntriesArgs, reply *AppendEntriesReply) {
	rf.mu.Lock()
	defer rf.mu.Unlock()

	reply.Term = rf.currentTerm
	reply.Success = false
	reply.ConflictIndex = -1
	reply.ConflictTerm = -1

	// Reply false if term < currentTerm
	if args.Term < rf.currentTerm {
		return
	}

	// If RPC request contains term T > currentTerm: set currentTerm = T, convert to follower
	if args.Term > rf.currentTerm {
		rf.currentTerm = args.Term
		rf.votedFor = InvalidId
		rf.persist()
	}

	rf.state = Follower
	rf.leaderId = args.LeaderId
	rf.resetElectionTimer()

	// Check if log contains an entry at prevLogIndex whose term matches prevLogTerm
	if args.PrevLogIndex < rf.lastIncludedIndex {
		reply.ConflictIndex = rf.lastIncludedIndex + 1
		return
	}

	if args.PrevLogIndex > rf.lastIncludedIndex+len(rf.log)-1 {
		reply.ConflictIndex = rf.lastIncludedIndex + len(rf.log)
		return
	}

	if args.PrevLogIndex > rf.lastIncludedIndex {
		logIndex := args.PrevLogIndex - rf.lastIncludedIndex
		if rf.log[logIndex].Term != args.PrevLogTerm {
			reply.ConflictTerm = rf.log[logIndex].Term
			// Find first index of conflicting term
			for i := logIndex; i >= 1; i-- {
				if rf.log[i].Term != reply.ConflictTerm {
					reply.ConflictIndex = i + 1 + rf.lastIncludedIndex
					break
				}
			}
			if reply.ConflictIndex == -1 {
				reply.ConflictIndex = rf.lastIncludedIndex + 1
			}
			return
		}
	}

	// If we reach here, prevLogIndex and prevLogTerm match
	reply.Success = true

	// Append new entries
	if len(args.Entries) > 0 {
		// Find insertion point
		insertIndex := args.PrevLogIndex + 1 - rf.lastIncludedIndex

		// Delete conflicting entries and append new ones
		for i, entry := range args.Entries {
			logIndex := insertIndex + i
			if logIndex < len(rf.log) {
				if rf.log[logIndex].Term != entry.Term {
					// Delete this entry and all that follow
					rf.log = rf.log[:logIndex]
					break
				}
			} else {
				break
			}
		}

		// Append new entries
		for i, entry := range args.Entries {
			logIndex := insertIndex + i
			if logIndex >= len(rf.log) {
				rf.log = append(rf.log, entry)
			}
		}

		rf.persist()
	}

	// Update commitIndex
	if args.LeaderCommit > rf.commitIndex {
		lastNewEntryIndex := args.PrevLogIndex + len(args.Entries)
		if args.LeaderCommit < lastNewEntryIndex {
			rf.commitIndex = args.LeaderCommit
		} else {
			rf.commitIndex = lastNewEntryIndex
		}
	}

	reply.Term = rf.currentTerm
}

// sendAppendEntries sends an AppendEntries RPC to a server
func (rf *Raft) sendAppendEntries(server int, args *AppendEntriesArgs, reply *AppendEntriesReply) bool {
	ok := rf.peers[server].Call("Raft.AppendEntries", args, reply)
	return ok
}

// Start is called by the service to submit a new command to the Raft log
func (rf *Raft) Start(command interface{}) (int, int, bool) {
	rf.mu.Lock()
	defer rf.mu.Unlock()

	if rf.state != Leader {
		return -1, rf.currentTerm, false
	}

	// Append entry to local log
	index := len(rf.log) + rf.lastIncludedIndex
	term := rf.currentTerm
	entry := LogEntry{Command: command, Term: term}
	rf.log = append(rf.log, entry)
	rf.persist()

	// Update leader's own nextIndex and matchIndex
	rf.nextIndex[rf.me] = index + 1
	rf.matchIndex[rf.me] = index

	// Start replication immediately
	go rf.broadcastAppendEntries()

	return index, term, true
}

// resetElectionTimer resets the election timeout
func (rf *Raft) resetElectionTimer() {
	if rf.electionTimer != nil {
		rf.electionTimer.Stop()
	}
	timeout := ElectionTimeout + time.Duration(rand.Intn(150))*time.Millisecond
	rf.electionTimer = time.NewTimer(timeout)
}

// resetHeartbeatTimer resets the heartbeat timer
func (rf *Raft) resetHeartbeatTimer() {
	if rf.heartbeatTimer != nil {
		rf.heartbeatTimer.Stop()
	}
	rf.heartbeatTimer = time.NewTimer(HeartBeatInterval)
}

// ticker is the main goroutine that handles timeouts
func (rf *Raft) ticker() {
	for !rf.killed() {
		select {
		case <-rf.electionTimer.C:
			rf.mu.Lock()
			if rf.state != Leader {
				rf.startElection()
			}
			rf.mu.Unlock()
		default:
			// Check if we have a heartbeat timer and it's expired
			if rf.heartbeatTimer != nil {
				select {
				case <-rf.heartbeatTimer.C:
					rf.mu.Lock()
					if rf.state == Leader {
						go rf.broadcastAppendEntries()
						rf.resetHeartbeatTimer()
					}
					rf.mu.Unlock()
				default:
				}
			}
		}
		time.Sleep(10 * time.Millisecond)
	}
}

// startElection initiates a new election
func (rf *Raft) startElection() {
	rf.state = Candidate
	rf.currentTerm++
	rf.votedFor = rf.me
	rf.leaderId = InvalidId
	rf.resetElectionTimer()
	rf.persist()

	lastLogIndex := len(rf.log) - 1 + rf.lastIncludedIndex
	lastLogTerm := rf.lastIncludedTerm
	if lastLogIndex > rf.lastIncludedIndex {
		lastLogTerm = rf.log[lastLogIndex-rf.lastIncludedIndex].Term
	}

	args := &RequestVoteArgs{
		Term:         rf.currentTerm,
		CandidateId:  rf.me,
		LastLogIndex: lastLogIndex,
		LastLogTerm:  lastLogTerm,
	}

	voteCount := int32(1) // Vote for self
	for i := range rf.peers {
		if i != rf.me {
			go func(server int) {
				reply := &RequestVoteReply{}
				if rf.sendRequestVote(server, args, reply) {
					rf.mu.Lock()
					defer rf.mu.Unlock()

					if rf.state != Candidate || rf.currentTerm != args.Term {
						return
					}

					if reply.Term > rf.currentTerm {
						rf.currentTerm = reply.Term
						rf.votedFor = InvalidId
						rf.state = Follower
						rf.leaderId = InvalidId
						rf.persist()
						return
					}

					if reply.VoteGranted {
						votes := atomic.AddInt32(&voteCount, 1)
						if int(votes) > len(rf.peers)/2 && rf.state == Candidate {
							rf.becomeLeader()
						}
					}
				}
			}(i)
		}
	}
}

// becomeLeader transitions this server to leader state
func (rf *Raft) becomeLeader() {
	if rf.state != Candidate {
		return
	}

	rf.state = Leader
	rf.leaderId = rf.me

	// Initialize leader state
	rf.nextIndex = make([]int, len(rf.peers))
	rf.matchIndex = make([]int, len(rf.peers))
	nextIndex := len(rf.log) + rf.lastIncludedIndex
	for i := range rf.peers {
		rf.nextIndex[i] = nextIndex
		rf.matchIndex[i] = 0
	}
	rf.matchIndex[rf.me] = nextIndex - 1

	// Stop election timer and start heartbeat timer
	if rf.electionTimer != nil {
		rf.electionTimer.Stop()
	}
	rf.resetHeartbeatTimer()

	// Send initial heartbeats
	go rf.broadcastAppendEntries()
}

// broadcastAppendEntries sends AppendEntries RPCs to all followers
func (rf *Raft) broadcastAppendEntries() {
	rf.mu.Lock()
	defer rf.mu.Unlock()

	if rf.state != Leader {
		return
	}

	for i := range rf.peers {
		if i != rf.me {
			go rf.sendAppendEntriesToPeer(i)
		}
	}
}

// sendAppendEntriesToPeer sends AppendEntries RPC to a specific peer
func (rf *Raft) sendAppendEntriesToPeer(server int) {
	rf.mu.Lock()
	if rf.state != Leader {
		rf.mu.Unlock()
		return
	}

	// Check if we need to send a snapshot
	if rf.nextIndex[server] <= rf.lastIncludedIndex {
		rf.sendSnapshotToPeer(server)
		rf.mu.Unlock()
		return
	}

	prevLogIndex := rf.nextIndex[server] - 1
	prevLogTerm := rf.lastIncludedTerm
	if prevLogIndex > rf.lastIncludedIndex {
		prevLogTerm = rf.log[prevLogIndex-rf.lastIncludedIndex].Term
	}

	// Prepare entries to send
	var entries []LogEntry
	if rf.nextIndex[server] < len(rf.log)+rf.lastIncludedIndex {
		startIndex := rf.nextIndex[server] - rf.lastIncludedIndex
		entries = make([]LogEntry, len(rf.log)-startIndex)
		copy(entries, rf.log[startIndex:])
	}

	args := &AppendEntriesArgs{
		Term:         rf.currentTerm,
		LeaderId:     rf.me,
		PrevLogIndex: prevLogIndex,
		PrevLogTerm:  prevLogTerm,
		Entries:      entries,
		LeaderCommit: rf.commitIndex,
	}
	rf.mu.Unlock()

	reply := &AppendEntriesReply{}
	if rf.sendAppendEntries(server, args, reply) {
		rf.mu.Lock()
		defer rf.mu.Unlock()

		if rf.state != Leader || rf.currentTerm != args.Term {
			return
		}

		if reply.Term > rf.currentTerm {
			rf.currentTerm = reply.Term
			rf.votedFor = InvalidId
			rf.state = Follower
			rf.leaderId = InvalidId
			rf.persist()
			return
		}

		if reply.Success {
			// Update nextIndex and matchIndex
			newMatchIndex := args.PrevLogIndex + len(args.Entries)
			rf.nextIndex[server] = newMatchIndex + 1
			rf.matchIndex[server] = max(rf.matchIndex[server], newMatchIndex)

			// Try to update commitIndex
			rf.updateCommitIndex()
		} else {
			// Handle log inconsistency
			if reply.ConflictIndex != -1 {
				rf.nextIndex[server] = reply.ConflictIndex
				if reply.ConflictTerm != -1 {
					// Find last entry with conflicting term
					for i := len(rf.log) - 1; i >= 0; i-- {
						if rf.log[i].Term == reply.ConflictTerm {
							rf.nextIndex[server] = i + 1 + rf.lastIncludedIndex
							break
						}
					}
				}
			} else {
				rf.nextIndex[server] = max(1, rf.nextIndex[server]-1)
			}

			// Retry immediately
			go rf.sendAppendEntriesToPeer(server)
		}
	}
}

// updateCommitIndex updates the commit index based on majority replication
func (rf *Raft) updateCommitIndex() {
	if rf.state != Leader {
		return
	}

	// Find the highest index that is replicated on a majority of servers
	for n := len(rf.log) - 1 + rf.lastIncludedIndex; n > rf.commitIndex; n-- {
		if n <= rf.lastIncludedIndex {
			break
		}

		// Check if log[n] is from current term
		if rf.log[n-rf.lastIncludedIndex].Term != rf.currentTerm {
			continue
		}

		count := 1 // Count self
		for i := range rf.peers {
			if i != rf.me && rf.matchIndex[i] >= n {
				count++
			}
		}

		if count > len(rf.peers)/2 {
			rf.commitIndex = n
			break
		}
	}
}

// applier applies committed entries to the state machine
func (rf *Raft) applier() {
	for !rf.killed() {
		rf.mu.Lock()

		// Apply committed entries
		for rf.lastApplied < rf.commitIndex {
			rf.lastApplied++
			if rf.lastApplied <= rf.lastIncludedIndex {
				continue
			}

			if rf.lastApplied-rf.lastIncludedIndex >= len(rf.log) {
				break
			}

			entry := rf.log[rf.lastApplied-rf.lastIncludedIndex]
			msg := ApplyMsg{
				CommandValid: true,
				Command:      entry.Command,
				CommandIndex: rf.lastApplied,
			}
			rf.mu.Unlock()

			select {
			case rf.applyCh <- msg:
			case <-time.After(100 * time.Millisecond):
				// Timeout to avoid blocking forever
			}

			rf.mu.Lock()
		}
		rf.mu.Unlock()
		time.Sleep(10 * time.Millisecond)
	}
}

// Helper function
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// InstallSnapshotArgs represents arguments for InstallSnapshot RPC
type InstallSnapshotArgs struct {
	Term              int    // leader's term
	LeaderId          int    // so follower can redirect clients
	LastIncludedIndex int    // the snapshot replaces all entries up through and including this index
	LastIncludedTerm  int    // term of lastIncludedIndex
	Data              []byte // raw bytes of the snapshot chunk
}

// InstallSnapshotReply represents reply for InstallSnapshot RPC
type InstallSnapshotReply struct {
	Term int // currentTerm, for leader to update itself
}

// InstallSnapshot RPC handler
func (rf *Raft) InstallSnapshot(args *InstallSnapshotArgs, reply *InstallSnapshotReply) {
	rf.mu.Lock()
	defer rf.mu.Unlock()

	reply.Term = rf.currentTerm

	// Reply immediately if term < currentTerm
	if args.Term < rf.currentTerm {
		return
	}

	// If RPC request contains term T > currentTerm: set currentTerm = T, convert to follower
	if args.Term > rf.currentTerm {
		rf.currentTerm = args.Term
		rf.votedFor = InvalidId
		rf.persist()
	}

	rf.state = Follower
	rf.leaderId = args.LeaderId
	rf.resetElectionTimer()

	// Reject if snapshot is older than our current snapshot
	if args.LastIncludedIndex <= rf.lastIncludedIndex {
		return
	}

	// Save snapshot and update state
	rf.snapshot = args.Data
	rf.lastIncludedIndex = args.LastIncludedIndex
	rf.lastIncludedTerm = args.LastIncludedTerm

	// Discard log entries covered by snapshot
	if args.LastIncludedIndex >= len(rf.log)+rf.lastIncludedIndex-1 {
		// Snapshot covers entire log
		rf.log = make([]LogEntry, 1)
		rf.log[0] = LogEntry{Term: args.LastIncludedTerm}
	} else {
		// Keep entries after snapshot
		newLog := make([]LogEntry, 1)
		newLog[0] = LogEntry{Term: args.LastIncludedTerm}
		startIndex := args.LastIncludedIndex - rf.lastIncludedIndex + 1
		newLog = append(newLog, rf.log[startIndex:]...)
		rf.log = newLog
	}

	rf.commitIndex = max(rf.commitIndex, args.LastIncludedIndex)
	rf.lastApplied = max(rf.lastApplied, args.LastIncludedIndex)

	// Persist state and snapshot
	rf.persister.SaveStateAndSnapshot(rf.encodeState(), rf.snapshot)

	// Apply snapshot to state machine
	msg := ApplyMsg{
		SnapshotValid: true,
		Snapshot:      rf.snapshot,
		SnapshotTerm:  rf.lastIncludedTerm,
		SnapshotIndex: rf.lastIncludedIndex,
	}

	go func() {
		rf.applyCh <- msg
	}()
}

// sendInstallSnapshot sends an InstallSnapshot RPC to a server
func (rf *Raft) sendInstallSnapshot(server int, args *InstallSnapshotArgs, reply *InstallSnapshotReply) bool {
	ok := rf.peers[server].Call("Raft.InstallSnapshot", args, reply)
	return ok
}

// sendSnapshotToPeer sends a snapshot to a specific peer
func (rf *Raft) sendSnapshotToPeer(server int) {
	args := &InstallSnapshotArgs{
		Term:              rf.currentTerm,
		LeaderId:          rf.me,
		LastIncludedIndex: rf.lastIncludedIndex,
		LastIncludedTerm:  rf.lastIncludedTerm,
		Data:              rf.snapshot,
	}

	go func() {
		reply := &InstallSnapshotReply{}
		if rf.sendInstallSnapshot(server, args, reply) {
			rf.mu.Lock()
			defer rf.mu.Unlock()

			if rf.state != Leader || rf.currentTerm != args.Term {
				return
			}

			if reply.Term > rf.currentTerm {
				rf.currentTerm = reply.Term
				rf.votedFor = InvalidId
				rf.state = Follower
				rf.leaderId = InvalidId
				rf.persist()
				return
			}

			// Update nextIndex and matchIndex
			rf.nextIndex[server] = args.LastIncludedIndex + 1
			rf.matchIndex[server] = args.LastIncludedIndex
		}
	}()
}

// Snapshot is called by the service to create a snapshot
func (rf *Raft) Snapshot(index int, snapshot []byte) {
	rf.mu.Lock()
	defer rf.mu.Unlock()

	// Ignore if snapshot is older than current snapshot
	if index <= rf.lastIncludedIndex {
		return
	}

	// Update snapshot state
	rf.snapshot = snapshot
	rf.lastIncludedTerm = rf.log[index-rf.lastIncludedIndex].Term
	rf.lastIncludedIndex = index

	// Trim log
	newLog := make([]LogEntry, 1)
	newLog[0] = LogEntry{Term: rf.lastIncludedTerm}
	if index < len(rf.log)+rf.lastIncludedIndex-1 {
		newLog = append(newLog, rf.log[index-rf.lastIncludedIndex+1:]...)
	}
	rf.log = newLog

	// Persist state and snapshot
	rf.persister.SaveStateAndSnapshot(rf.encodeState(), rf.snapshot)
}

// encodeState encodes the persistent state
func (rf *Raft) encodeState() []byte {
	w := new(bytes.Buffer)
	e := gob.NewEncoder(w)
	e.Encode(rf.currentTerm)
	e.Encode(rf.votedFor)
	e.Encode(rf.log)
	e.Encode(rf.lastIncludedIndex)
	e.Encode(rf.lastIncludedTerm)
	return w.Bytes()
}
