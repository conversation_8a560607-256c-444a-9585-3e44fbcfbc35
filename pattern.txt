按照如下提纲，完成大作业报告，描述并记录所有的操作步骤，以及相关实验代码、实现截图。

第一项大作业：分布式实时电商推荐系统
1）分布式 Kafka环境配置（15分）
完成 Kafka环境，实现消息订阅、发布、Topic创建等功能。
2）分布式 Flink环境配置（15分）
完成 Flink环境搭建，实现从 Kafka处消费数据、进行实时数据处理、并将结果发布到 Kafka。
3）实现推荐系统工程（10分）
完成推荐系统工程的创建，在 Flink中现任意的推荐算法。
4）实现消息源软件工程（10分）
完成消息源软件，实现整体的业务流程，模拟完整的分布式实时推荐系统。

第二项大作业：Raft一致性协议
1）Raft选举（10分）
阅读实验要求，实现Raft领导者选举和心跳机制（无日志条目的AppendEntries RPC），并保证代码通过测试用例。
2）Raft日志（10分）
阅读实验要求，实现领导者和跟随者日志复制过程的代码，实现在分布式节点上追加新的日志条目，并保证代码通过测试用例。
3）Raft持久化（10分）
阅读实验要求，通过添加保存和恢复持久化状态的代码，完成raft.go中的函数persist() 和 readPersist()，并保证代码通过测试用例。
4）Raft日志压缩（10分）
阅读实验要求，实现Snapshot()和InstallSnapshot RPC，并保证代码通过测试用例。
